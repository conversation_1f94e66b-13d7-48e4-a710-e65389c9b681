@echo off
chcp 65001 >nul
echo ========================================
echo OneRouter API Key 自动化获取工具
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo 检查依赖包...
pip show playwright >nul 2>&1
if errorlevel 1 (
    echo 📦 安装依赖包...
    pip install -r requirements.txt
    echo 📦 安装浏览器...
    playwright install chromium
)

echo 🚀 启动自动化脚本...
echo.
python onerouter_automation.py

echo.
echo 执行完成！
pause
