#!/usr/bin/env node
/**
 * 生成 Claude 4.0 sonnet 可直接执行的 MCP 命令序列
 * 这个脚本会读取下一个账号并生成完整的自动化指令
 */

const fs = require('fs').promises;

class ClaudeCommandGenerator {
    constructor() {
        this.cardFile = '卡密信息_XJL2506300700BXW78.txt';
    }

    async getNextAccount() {
        try {
            const content = await fs.readFile(this.cardFile, 'utf-8');
            const lines = content.split('\n').filter(line => line.trim() && line.startsWith('卡密：'));
            
            if (lines.length === 0) {
                return null;
            }

            const firstLine = lines[0];
            const cardInfo = firstLine.replace('卡密：', '').trim();
            const parts = cardInfo.split('----');
            
            if (parts.length >= 2) {
                return {
                    email: parts[0].trim(),
                    password: parts[1].trim(),
                    auxiliary: parts[2] ? parts[2].trim() : '',
                    original: firstLine
                };
            }
            
            return null;
        } catch (error) {
            console.error('❌ 读取卡密文件失败:', error.message);
            return null;
        }
    }

    async generateCommands() {
        const account = await this.getNextAccount();
        if (!account) {
            console.log('❌ 没有更多待处理的账号');
            return;
        }

        const keyName = `${account.email.split('@')[0]} Main Key`;
        
        console.log('🤖 Claude 4.0 sonnet 自动化命令生成器');
        console.log('=' * 60);
        console.log(`📧 账号: ${account.email}`);
        console.log(`🔑 密码: ${account.password}`);
        console.log(`📨 辅助邮箱: ${account.auxiliary}`);
        console.log(`🏷️  API Key 名称: ${keyName}`);
        console.log('=' * 60);
        
        console.log('\n请在 Claude 4.0 sonnet 中按顺序执行以下命令：\n');

        // 生成命令序列
        const commands = [
            {
                step: 1,
                description: '关闭浏览器',
                command: `<function_calls>
<invoke name="browser_close_playwright">
