#!/usr/bin/env python3
"""
OneRouter API Key 半自动化获取工具
手动登录 + 自动化操作，避免验证码问题
"""

import time
import json
import os
import webbrowser
from datetime import datetime
from pathlib import Path

class OneRouterManualAssist:
    def __init__(self):
        self.output_dir = Path("api_keys")
        self.output_dir.mkdir(exist_ok=True)
        
    def read_accounts(self):
        """读取账号信息"""
        accounts = []
        if not os.path.exists("accounts.txt"):
            print("❌ 账号文件 accounts.txt 不存在")
            return accounts
            
        with open("accounts.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            if '----' in line:
                parts = line.split('----')
            elif ':' in line:
                parts = line.split(':')
            else:
                continue
                
            if len(parts) >= 2:
                email = parts[0].strip()
                password = parts[1].strip()
                accounts.append({'email': email, 'password': password})
                
        print(f"📖 读取到 {len(accounts)} 个账号")
        return accounts
    
    def save_api_key(self, email, api_key):
        """保存API Key"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"api_key_{email.replace('@', '_').replace('.', '_')}_{timestamp}.txt"
        filepath = self.output_dir / filename
        
        content = f"""OneRouter API Key (手动辅助版本)
===================

Account: {email}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Status: Active

API Key: {api_key}

Note: This key was generated with manual assistance.
Please keep this key secure and do not share it publicly.
"""
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
            
        print(f"💾 API Key已保存到: {filepath}")
        return filepath
    
    def remove_processed_account(self, email):
        """从账号列表删除已处理的账号"""
        if not os.path.exists("accounts.txt"):
            return
            
        with open("accounts.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        filtered_lines = [line for line in lines if email not in line]
        
        with open("accounts.txt", 'w', encoding='utf-8') as f:
            f.writelines(filtered_lines)
            
        print(f"🗑️  已从账号列表中删除: {email}")
    
    def process_account_manual(self, account):
        """手动辅助处理单个账号"""
        email = account['email']
        password = account['password']
        
        print(f"\n{'='*60}")
        print(f"🚀 开始处理账号: {email}")
        print(f"密码: {password}")
        print(f"{'='*60}")
        
        # 步骤1：打开谷歌登录页面
        print("\n📋 步骤1: 谷歌账号登录")
        print("🌐 正在打开谷歌登录页面...")
        webbrowser.open("https://accounts.google.com/signin")
        
        print(f"📧 请手动登录谷歌账号: {email}")
        print(f"🔑 密码: {password}")
        input("✅ 登录完成后，请按回车继续...")
        
        # 步骤2：打开OneRouter登录页面
        print("\n📋 步骤2: OneRouter平台登录")
        print("🌐 正在打开OneRouter登录页面...")
        webbrowser.open("https://app.onerouter.pro/login")
        
        print("🔗 请点击 'Sign in with Google' 按钮")
        print("🔗 选择刚才登录的谷歌账号")
        input("✅ OneRouter登录完成后，请按回车继续...")
        
        # 步骤3：打开API Keys页面
        print("\n📋 步骤3: 创建API Key")
        print("🌐 正在打开API Keys页面...")
        webbrowser.open("https://app.onerouter.pro/apiKeys")
        
        print("🔑 请按照以下步骤创建API Key:")
        print("   1. 点击创建新Key按钮 (通常是 'N' 或 '+' 按钮)")
        print("   2. 输入Key名称 (例如: Auto Key)")
        print("   3. 点击 'Create' 按钮")
        print("   4. 复制生成的API Key")
        
        # 获取API Key
        while True:
            api_key = input("\n🔑 请粘贴生成的API Key (以sk-开头): ").strip()
            
            if api_key.startswith('sk-') and len(api_key) > 20:
                break
            else:
                print("❌ API Key格式不正确，请重新输入")
        
        # 保存API Key
        self.save_api_key(email, api_key)
        
        # 从账号列表删除
        self.remove_processed_account(email)
        
        print(f"🎉 账号 {email} 处理完成！")
        return True
    
    def run(self):
        """主执行函数"""
        print("🤖 OneRouter API Key 半自动化获取工具")
        print("=" * 60)
        print("💡 此工具将引导您手动完成登录，避免验证码问题")
        print("=" * 60)
        
        accounts = self.read_accounts()
        if not accounts:
            print("❌ 没有找到可处理的账号")
            return
        
        success_count = 0
        
        for i, account in enumerate(accounts, 1):
            print(f"\n📊 总进度: {i}/{len(accounts)}")
            
            try:
                if self.process_account_manual(account):
                    success_count += 1
                    
                    if i < len(accounts):
                        print(f"\n⏳ 准备处理下一个账号...")
                        choice = input("继续处理下一个账号? (y/n): ").lower()
                        if choice != 'y':
                            break
                        
            except KeyboardInterrupt:
                print("\n⚠️  用户中断操作")
                break
            except Exception as e:
                print(f"❌ 处理账号时发生错误: {str(e)}")
        
        print(f"\n{'='*60}")
        print(f"🎯 执行完成！")
        print(f"✅ 成功处理: {success_count} 个账号")
        print(f"📁 结果文件保存在: {self.output_dir}")
        print(f"{'='*60}")


if __name__ == "__main__":
    automation = OneRouterManualAssist()
    automation.run()
