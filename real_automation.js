#!/usr/bin/env node
/**
 * OneRouter 真实自动化脚本
 * 直接调用 Claude 4.0 sonnet 的 MCP 工具
 */

const fs = require('fs').promises;

class RealOneRouterAutomation {
    constructor() {
        this.cardFile = '卡密信息_XJL2506300700BXW78.txt';
        this.apiKeyFile = 'onerouter_api_key.txt';
        this.currentAccount = null;
    }

    /**
     * 读取下一个待处理账号
     */
    async getNextAccount() {
        try {
            const content = await fs.readFile(this.cardFile, 'utf-8');
            const lines = content.split('\n').filter(line => line.trim() && line.startsWith('卡密：'));
            
            if (lines.length === 0) {
                console.log('❌ 没有更多待处理的账号');
                return null;
            }

            const firstLine = lines[0];
            const cardInfo = firstLine.replace('卡密：', '').trim();
            const parts = cardInfo.split('----');
            
            if (parts.length >= 2) {
                return {
                    email: parts[0].trim(),
                    password: parts[1].trim(),
                    auxiliary: parts[2] ? parts[2].trim() : '',
                    original: firstLine
                };
            }
            
            return null;
        } catch (error) {
            console.error('❌ 读取卡密文件失败:', error.message);
            return null;
        }
    }

    /**
     * 执行完整的自动化流程
     */
    async executeAutomation() {
        console.log('🤖 OneRouter 真实自动化开始');
        console.log('=' * 50);

        // 获取下一个账号
        this.currentAccount = await this.getNextAccount();
        if (!this.currentAccount) {
            console.log('✅ 所有账号已处理完成');
            return;
        }

        console.log(`📋 处理账号: ${this.currentAccount.email}`);
        console.log(`🔑 密码: ${this.currentAccount.password}`);
        console.log(`📧 辅助邮箱: ${this.currentAccount.auxiliary}`);

        try {
            // 执行自动化步骤
            await this.step1_closeBrowser();
            await this.step2_loginGoogle();
            await this.step3_accessOneRouter();
            await this.step4_createApiKey();
            await this.step5_saveResults();
            
            console.log('🎉 账号处理完成！');
            
        } catch (error) {
            console.error('❌ 自动化执行失败:', error.message);
            throw error;
        }
    }

    /**
     * 步骤1: 关闭浏览器
     */
    async step1_closeBrowser() {
        console.log('\n📋 步骤1: 关闭浏览器');
        
        // 这里我们需要实际调用 Claude 的 MCP 工具
        // 由于我们在 Node.js 环境中，我们需要通过某种方式与 Claude 通信
        
        // 方案: 生成指令文件让用户在 Claude 中执行
        const instruction = {
            step: 1,
            action: 'browser_close_playwright',
            description: '关闭浏览器，开启新会话',
            mcpCall: 'browser_close_playwright',
            parameters: {}
        };
        
        await this.logInstruction(instruction);
        return true;
    }

    /**
     * 步骤2: 登录谷歌账号
     */
    async step2_loginGoogle() {
        console.log('\n📋 步骤2: 登录谷歌账号');
        
        const instructions = [
            {
                step: '2.1',
                action: 'browser_navigate_playwright',
                description: '访问谷歌退出页面',
                mcpCall: 'browser_navigate_playwright',
                parameters: { url: 'https://accounts.google.com/logout' }
            },
            {
                step: '2.2',
                action: 'browser_click_playwright',
                description: '点击使用其他账号',
                mcpCall: 'browser_click_playwright',
                parameters: { element: '使用其他账号', ref: 'e74' }
            },
            {
                step: '2.3',
                action: 'browser_type_playwright',
                description: '输入邮箱地址',
                mcpCall: 'browser_type_playwright',
                parameters: { 
                    element: '邮箱输入框', 
                    ref: 'e148', 
                    text: this.currentAccount.email 
                }
            },
            {
                step: '2.4',
                action: 'browser_click_playwright',
                description: '点击下一步',
                mcpCall: 'browser_click_playwright',
                parameters: { element: '下一步按钮', ref: 'e168' }
            },
            {
                step: '2.5',
                action: 'browser_type_playwright',
                description: '输入密码',
                mcpCall: 'browser_type_playwright',
                parameters: { 
                    element: '密码输入框', 
                    ref: 'e226', 
                    text: this.currentAccount.password 
                }
            },
            {
                step: '2.6',
                action: 'browser_click_playwright',
                description: '完成登录',
                mcpCall: 'browser_click_playwright',
                parameters: { element: '下一步按钮', ref: 'e259' }
            },
            {
                step: '2.7',
                action: 'browser_wait_for_playwright',
                description: '等待登录完成',
                mcpCall: 'browser_wait_for_playwright',
                parameters: { time: 5 }
            }
        ];

        for (const instruction of instructions) {
            await this.logInstruction(instruction);
        }
        
        return true;
    }

    /**
     * 步骤3: 访问 OneRouter
     */
    async step3_accessOneRouter() {
        console.log('\n📋 步骤3: 访问 OneRouter');
        
        const instructions = [
            {
                step: '3.1',
                action: 'browser_navigate_playwright',
                description: '访问 OneRouter 登录页面',
                mcpCall: 'browser_navigate_playwright',
                parameters: { url: 'https://app.onerouter.pro/login' }
            },
            {
                step: '3.2',
                action: 'browser_click_playwright',
                description: '点击谷歌登录',
                mcpCall: 'browser_click_playwright',
                parameters: { element: 'Sign in with Google 按钮', ref: 'e14' }
            },
            {
                step: '3.3',
                action: 'browser_wait_for_playwright',
                description: '等待OAuth跳转',
                mcpCall: 'browser_wait_for_playwright',
                parameters: { time: 5 }
            },
            {
                step: '3.4',
                action: 'browser_click_playwright',
                description: '选择当前账号',
                mcpCall: 'browser_click_playwright',
                parameters: { element: '当前账号', ref: 'e61' }
            },
            {
                step: '3.5',
                action: 'browser_click_playwright',
                description: '授权OneRouter',
                mcpCall: 'browser_click_playwright',
                parameters: { element: 'Continue 按钮', ref: 'e63' }
            },
            {
                step: '3.6',
                action: 'browser_wait_for_playwright',
                description: '等待OneRouter登录',
                mcpCall: 'browser_wait_for_playwright',
                parameters: { time: 10 }
            }
        ];

        for (const instruction of instructions) {
            await this.logInstruction(instruction);
        }
        
        return true;
    }

    /**
     * 步骤4: 创建 API Key
     */
    async step4_createApiKey() {
        console.log('\n📋 步骤4: 创建 API Key');
        
        const keyName = `${this.currentAccount.email.split('@')[0]} Main Key`;
        
        const instructions = [
            {
                step: '4.1',
                action: 'browser_click_playwright',
                description: '点击创建API Key',
                mcpCall: 'browser_click_playwright',
                parameters: { element: 'Create new API Key 按钮', ref: 'e610' }
            },
            {
                step: '4.2',
                action: 'browser_type_playwright',
                description: '输入API Key名称',
                mcpCall: 'browser_type_playwright',
                parameters: { 
                    element: 'API Key 名称输入框', 
                    ref: 'e631', 
                    text: keyName 
                }
            },
            {
                step: '4.3',
                action: 'browser_click_playwright',
                description: '创建API Key',
                mcpCall: 'browser_click_playwright',
                parameters: { element: 'Create 按钮', ref: 'e634' }
            },
            {
                step: '4.4',
                action: 'browser_wait_for_playwright',
                description: '等待API Key生成',
                mcpCall: 'browser_wait_for_playwright',
                parameters: { time: 3 }
            },
            {
                step: '4.5',
                action: 'browser_snapshot_playwright',
                description: '获取页面快照以提取API Key',
                mcpCall: 'browser_snapshot_playwright',
                parameters: {}
            },
            {
                step: '4.6',
                action: 'browser_click_playwright',
                description: '确认API Key创建',
                mcpCall: 'browser_click_playwright',
                parameters: { element: 'Confirm 按钮', ref: 'e671' }
            }
        ];

        for (const instruction of instructions) {
            await this.logInstruction(instruction);
        }
        
        return true;
    }

    /**
     * 步骤5: 保存结果
     */
    async step5_saveResults() {
        console.log('\n📋 步骤5: 保存结果');
        
        // 这里需要手动输入API Key
        console.log('⚠️  请手动从浏览器中复制API Key，然后运行保存脚本');
        console.log(`📝 运行命令: node save_api_key.js "${this.currentAccount.email}" "YOUR_API_KEY_HERE"`);
        
        return true;
    }

    /**
     * 记录指令
     */
    async logInstruction(instruction) {
        console.log(`   ${instruction.step}. ${instruction.description}`);
        console.log(`      MCP调用: ${instruction.mcpCall}`);
        if (Object.keys(instruction.parameters).length > 0) {
            console.log(`      参数: ${JSON.stringify(instruction.parameters, null, 8)}`);
        }
        console.log('');
        
        // 保存到指令文件
        const instructionFile = `automation_instructions_${Date.now()}.json`;
        await fs.writeFile(instructionFile, JSON.stringify(instruction, null, 2), 'utf-8');
        
        return true;
    }
}

// 程序入口
if (require.main === module) {
    const automation = new RealOneRouterAutomation();
    automation.executeAutomation().catch(console.error);
}

module.exports = RealOneRouterAutomation;
