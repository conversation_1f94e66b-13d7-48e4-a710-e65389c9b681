#!/usr/bin/env python3
"""
OneRouter API Key 自动化获取脚本 (增强版)
支持配置文件、重试机制、更好的错误处理
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError

class OneRouterAutomationEnhanced:
    def __init__(self, config_file="config.json"):
        self.config = self.load_config(config_file)
        self.output_dir = Path(self.config['settings']['output_directory'])
        self.output_dir.mkdir(exist_ok=True)
        
        # 结果记录
        self.success_log = []
        self.failed_log = []
        
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ 配置文件 {config_file} 不存在，使用默认配置")
            return self.get_default_config()
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e}")
            return self.get_default_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "settings": {
                "accounts_file": "accounts.txt",
                "output_directory": "api_keys",
                "browser_headless": False,
                "wait_between_accounts": 10,
                "timeout_seconds": 30,
                "max_retries": 3
            },
            "urls": {
                "google_login": "https://accounts.google.com/signin",
                "onerouter_login": "https://app.onerouter.pro/login",
                "onerouter_apikeys": "https://app.onerouter.pro/apiKeys"
            },
            "selectors": {
                "google_email_input": "input[type=\"email\"]",
                "google_password_input": "input[type=\"password\"]",
                "google_next_button": "button:has-text(\"下一步\"), button:has-text(\"Next\")",
                "onerouter_google_login": "button:has-text(\"Sign in with Google\")",
                "onerouter_continue": "button:has-text(\"Continue\"), button:has-text(\"继续\")",
                "create_key_button": "button:has-text(\"N\"), button[aria-label*=\"new\"]",
                "key_name_input": "input[placeholder*=\"Chatbot\"], input[placeholder*=\"Key\"]",
                "create_submit": "button:has-text(\"Create\"), button:has-text(\"创建\")",
                "api_key_display": "input[value*=\"sk-\"], textarea[value*=\"sk-\"]",
                "confirm_button": "button:has-text(\"Confirm\"), button:has-text(\"确认\")"
            }
        }
    
    def read_accounts(self):
        """读取账号信息文件"""
        accounts_file = self.config['settings']['accounts_file']
        accounts = []
        
        if not os.path.exists(accounts_file):
            print(f"❌ 账号文件 {accounts_file} 不存在")
            return accounts
            
        with open(accounts_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # 解析格式：email----password 或 email:password
            if '----' in line:
                parts = line.split('----')
            elif ':' in line:
                parts = line.split(':')
            else:
                print(f"⚠️  第{line_num}行格式错误，跳过: {line}")
                continue
                
            if len(parts) >= 2:
                email = parts[0].strip()
                password = parts[1].strip()
                accounts.append({'email': email, 'password': password, 'line_num': line_num})
            else:
                print(f"⚠️  第{line_num}行格式错误，跳过: {line}")
                
        print(f"📖 读取到 {len(accounts)} 个账号")
        return accounts
    
    def remove_processed_account(self, email):
        """从账号文件中删除已处理的账号"""
        accounts_file = self.config['settings']['accounts_file']
        
        if not os.path.exists(accounts_file):
            return
            
        with open(accounts_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 过滤掉包含该邮箱的行
        filtered_lines = []
        for line in lines:
            if email not in line:
                filtered_lines.append(line)
                
        with open(accounts_file, 'w', encoding='utf-8') as f:
            f.writelines(filtered_lines)
            
        print(f"🗑️  已从账号列表中删除: {email}")
    
    async def safe_click(self, page, selector, timeout=None):
        """安全点击元素"""
        if timeout is None:
            timeout = self.config['settings']['timeout_seconds'] * 1000
            
        try:
            element = page.locator(selector).first
            await element.wait_for(state='visible', timeout=timeout)
            await element.click()
            return True
        except Exception as e:
            print(f"⚠️  点击元素失败 {selector}: {e}")
            return False
    
    async def safe_fill(self, page, selector, text, timeout=None):
        """安全填写文本"""
        if timeout is None:
            timeout = self.config['settings']['timeout_seconds'] * 1000

        try:
            # 尝试多个选择器策略
            selectors = selector.split(', ')
            for sel in selectors:
                try:
                    element = page.locator(sel).first
                    await element.wait_for(state='visible', timeout=5000)
                    await element.fill(text)
                    return True
                except:
                    continue

            print(f"⚠️  所有选择器都失败了: {selector}")
            return False
        except Exception as e:
            print(f"⚠️  填写文本失败 {selector}: {e}")
            return False

    async def login_google_enhanced(self, page, email, password):
        """增强版谷歌登录"""
        selectors = self.config['selectors']
        urls = self.config['urls']

        try:
            print(f"🔐 开始登录谷歌账号: {email}")

            # 访问谷歌登录页面
            await page.goto(urls['google_login'], timeout=30000)
            await page.wait_for_load_state('networkidle')

            # 输入邮箱
            if not await self.safe_fill(page, selectors['google_email_input'], email):
                return False

            if not await self.safe_click(page, selectors['google_next_button']):
                return False

            await page.wait_for_load_state('networkidle')
            await page.wait_for_timeout(2000)  # 等待页面完全加载

            # 输入密码 - 使用更强健的选择器
            password_selectors = [
                'input[type="password"]:not([aria-hidden="true"])',
                'input[name="password"]',
                'input[autocomplete="current-password"]',
                '#password input',
                '[data-form-type="password"] input'
            ]

            password_filled = False
            for selector in password_selectors:
                try:
                    element = page.locator(selector).first
                    if await element.is_visible():
                        await element.fill(password)
                        password_filled = True
                        print(f"✅ 密码输入成功，使用选择器: {selector}")
                        break
                except:
                    continue

            if not password_filled:
                print("❌ 无法找到密码输入框")
                return False

            if not await self.safe_click(page, selectors['google_next_button']):
                return False

            await page.wait_for_load_state('networkidle')

            # 等待登录完成
            await page.wait_for_url('**/myaccount.google.com/**', timeout=30000)
            print(f"✅ 谷歌登录成功: {email}")
            return True

        except Exception as e:
            print(f"❌ 谷歌登录失败: {email} - {str(e)}")
            return False

    async def login_onerouter_enhanced(self, page):
        """增强版OneRouter登录"""
        selectors = self.config['selectors']
        urls = self.config['urls']

        try:
            print("🌐 开始登录OneRouter平台")

            # 访问OneRouter登录页面
            await page.goto(urls['onerouter_login'], timeout=30000)
            await page.wait_for_load_state('networkidle')

            # 点击Google登录按钮
            if not await self.safe_click(page, selectors['onerouter_google_login']):
                return False

            await page.wait_for_load_state('networkidle')

            # 处理Google授权页面
            if 'accounts.google.com' in page.url:
                # 选择账号（如果有多个）
                account_selector = page.locator('div[data-identifier]').first
                if await account_selector.is_visible():
                    await account_selector.click()
                    await page.wait_for_load_state('networkidle')

                # 点击继续授权
                await self.safe_click(page, selectors['onerouter_continue'])
                await page.wait_for_load_state('networkidle')

            # 等待跳转到OneRouter主页
            await page.wait_for_url('**/app.onerouter.pro/**', timeout=30000)
            print("✅ OneRouter登录成功")
            return True

        except Exception as e:
            print(f"❌ OneRouter登录失败: {str(e)}")
            return False

    async def create_api_key_enhanced(self, page, email):
        """增强版API Key创建"""
        selectors = self.config['selectors']
        urls = self.config['urls']

        try:
            print("🔑 开始创建API Key")

            # 导航到API Keys页面
            await page.goto(urls['onerouter_apikeys'], timeout=30000)
            await page.wait_for_load_state('networkidle')

            # 点击创建新Key按钮
            if not await self.safe_click(page, selectors['create_key_button']):
                return None

            await page.wait_for_timeout(2000)

            # 输入Key名称
            key_name = f"Auto Key {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if not await self.safe_fill(page, selectors['key_name_input'], key_name):
                return None

            # 点击创建按钮
            if not await self.safe_click(page, selectors['create_submit']):
                return None

            await page.wait_for_timeout(3000)

            # 获取API Key
            api_key_element = page.locator(selectors['api_key_display']).first
            if await api_key_element.is_visible():
                api_key = await api_key_element.input_value()
                print(f"✅ API Key创建成功: {api_key[:20]}...")

                # 点击确认按钮
                await self.safe_click(page, selectors['confirm_button'])

                return api_key
            else:
                print("❌ 无法获取API Key")
                return None

        except Exception as e:
            print(f"❌ 创建API Key失败: {str(e)}")
            return None

    def save_api_key(self, email, api_key, account_name=""):
        """保存API Key到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"api_key_{email.replace('@', '_').replace('.', '_')}_{timestamp}.txt"
        filepath = self.output_dir / filename

        content = f"""OneRouter API Key
===================

Account: {email}
Account Name: {account_name}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Status: Active

API Key: {api_key}

Note: This key was automatically generated by enhanced automation script.
Please keep this key secure and do not share it publicly.
"""

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"💾 API Key已保存到: {filepath}")
        return filepath

    async def process_account_with_retry(self, browser, account):
        """带重试机制的账号处理"""
        max_retries = self.config['settings']['max_retries']

        for attempt in range(max_retries):
            if attempt > 0:
                print(f"🔄 第 {attempt + 1} 次重试...")
                await asyncio.sleep(5)  # 重试前等待

            success = await self.process_account_enhanced(browser, account)
            if success:
                return True

        print(f"❌ 账号 {account['email']} 在 {max_retries} 次尝试后仍然失败")
        return False

    async def process_account_enhanced(self, browser, account):
        """增强版账号处理"""
        email = account['email']
        password = account['password']

        print(f"\n{'='*50}")
        print(f"🚀 开始处理账号: {email}")
        print(f"{'='*50}")

        context = await browser.new_context()
        page = await context.new_page()

        try:
            # 1. 登录谷歌
            if not await self.login_google_enhanced(page, email, password):
                self.failed_log.append({
                    'email': email,
                    'error': 'Google login failed',
                    'timestamp': datetime.now().isoformat()
                })
                return False

            # 2. 登录OneRouter
            if not await self.login_onerouter_enhanced(page):
                self.failed_log.append({
                    'email': email,
                    'error': 'OneRouter login failed',
                    'timestamp': datetime.now().isoformat()
                })
                return False

            # 3. 创建API Key
            api_key = await self.create_api_key_enhanced(page, email)
            if not api_key:
                self.failed_log.append({
                    'email': email,
                    'error': 'API Key creation failed',
                    'timestamp': datetime.now().isoformat()
                })
                return False

            # 4. 保存API Key
            filepath = self.save_api_key(email, api_key)

            # 5. 记录成功
            self.success_log.append({
                'email': email,
                'api_key': api_key,
                'file_path': str(filepath),
                'timestamp': datetime.now().isoformat()
            })

            # 6. 从账号列表中删除
            self.remove_processed_account(email)

            print(f"🎉 账号 {email} 处理完成！")
            return True

        except Exception as e:
            print(f"❌ 处理账号 {email} 时发生错误: {str(e)}")
            self.failed_log.append({
                'email': email,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False

        finally:
            await context.close()

    def save_logs(self):
        """保存执行日志"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存成功日志
        if self.success_log:
            success_file = self.output_dir / f"success_log_{timestamp}.json"
            with open(success_file, 'w', encoding='utf-8') as f:
                json.dump(self.success_log, f, ensure_ascii=False, indent=2)
            print(f"📊 成功日志已保存: {success_file}")

        # 保存失败日志
        if self.failed_log:
            failed_file = self.output_dir / f"failed_log_{timestamp}.json"
            with open(failed_file, 'w', encoding='utf-8') as f:
                json.dump(self.failed_log, f, ensure_ascii=False, indent=2)
            print(f"📊 失败日志已保存: {failed_file}")

    async def run(self):
        """主执行函数"""
        print("🤖 OneRouter API Key 自动化获取工具 (增强版)")
        print("=" * 60)

        # 读取账号列表
        accounts = self.read_accounts()
        if not accounts:
            print("❌ 没有找到可处理的账号")
            return

        settings = self.config['settings']

        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=settings['browser_headless'],
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )

            try:
                success_count = 0
                failed_count = 0

                for i, account in enumerate(accounts, 1):
                    print(f"\n📋 进度: {i}/{len(accounts)}")

                    if await self.process_account_with_retry(browser, account):
                        success_count += 1
                    else:
                        failed_count += 1

                    # 每个账号之间等待一段时间，避免被检测
                    if i < len(accounts):
                        wait_time = settings['wait_between_accounts']
                        print(f"⏳ 等待 {wait_time} 秒后处理下一个账号...")
                        await asyncio.sleep(wait_time)

                # 输出最终统计
                print(f"\n{'='*60}")
                print(f"🎯 执行完成！")
                print(f"✅ 成功: {success_count} 个账号")
                print(f"❌ 失败: {failed_count} 个账号")
                print(f"📁 结果文件保存在: {self.output_dir}")
                print(f"{'='*60}")

                # 保存日志
                self.save_logs()

            finally:
                await browser.close()


async def main():
    """程序入口"""
    print("🚀 OneRouter API Key 批量获取工具 (增强版)")
    print("作者: Claude 4.0 sonnet")
    print("=" * 60)

    # 创建自动化实例并运行
    automation = OneRouterAutomationEnhanced()
    await automation.run()


if __name__ == "__main__":
    asyncio.run(main())
