# OneRouter API Key 自动化获取工具 - 使用指南

## ⚠️ **重要更新**

由于 Playwright 自动化浏览器会触发谷歌验证码，我们提供了两个更好的解决方案：

## 🎯 快速开始

### 推荐方案：半自动化工具 (无需安装额外依赖)
```bash
# 直接运行，无需安装任何依赖
python onerouter_manual_assist.py
```

### 备选方案：Selenium版本 (需要ChromeDriver)
```bash
# 安装Selenium
pip install selenium

# 下载并配置ChromeDriver
# 然后运行
python onerouter_selenium.py
```

### 2. 配置账号
编辑 `accounts.txt` 文件，添加您的谷歌账号：
```
# 格式: 邮箱----密码
<EMAIL>----your_password
<EMAIL>----another_password
```

### 3. 运行脚本
```bash
# 方式1: 直接运行Python脚本
python onerouter_automation_enhanced.py

# 方式2: 使用批处理文件 (Windows)
run.bat

# 方式3: 运行基础版本
python onerouter_automation.py
```

## 📁 文件说明

| 文件名 | 说明 |
|--------|------|
| `onerouter_automation.py` | 基础版自动化脚本 |
| `onerouter_automation_enhanced.py` | 增强版脚本（推荐） |
| `accounts.txt` | 账号列表文件 |
| `config.json` | 配置文件 |
| `requirements.txt` | Python依赖 |
| `run.bat` | Windows启动脚本 |
| `test_config.py` | 配置测试脚本 |

## ⚙️ 配置选项

编辑 `config.json` 来自定义设置：

```json
{
  "settings": {
    "accounts_file": "accounts.txt",        // 账号文件路径
    "output_directory": "api_keys",         // 输出目录
    "browser_headless": false,              // 是否无头模式
    "wait_between_accounts": 10,            // 账号间等待时间(秒)
    "timeout_seconds": 30,                  // 操作超时时间
    "max_retries": 3                        // 最大重试次数
  }
}
```

## 🔧 高级功能

### 无头模式运行
将 `config.json` 中的 `browser_headless` 设置为 `true`：
```json
"browser_headless": true
```

### 自定义等待时间
调整账号间的等待时间以避免被检测：
```json
"wait_between_accounts": 15
```

### 增加重试次数
对于网络不稳定的环境：
```json
"max_retries": 5
```

## 📊 结果查看

### 输出文件
- `api_keys/api_key_xxx.txt` - 每个账号的API Key文件
- `api_keys/success_log_xxx.json` - 成功处理的日志
- `api_keys/failed_log_xxx.json` - 失败处理的日志

### API Key文件格式
```
OneRouter API Key
===================

Account: <EMAIL>
Generated: 2025-06-30 15:30:45
Status: Active

API Key: sk-1TD4PFwiRQ2tU08E36F7F2AcDa1447B888D3C133F87b1605
```

## 🛠️ 故障排除

### 常见问题

1. **登录失败**
   - 检查账号密码是否正确
   - 确认账号没有开启二次验证
   - 检查网络连接

2. **元素定位失败**
   - OneRouter页面可能有更新
   - 可以修改 `config.json` 中的选择器

3. **浏览器启动失败**
   ```bash
   playwright install chromium
   ```

4. **依赖包问题**
   ```bash
   pip install --upgrade playwright
   ```

### 测试脚本
运行测试确保一切正常：
```bash
python test_config.py
```

## 🔒 安全注意事项

1. **账号安全**
   - 不要在公共场所运行脚本
   - 定期更换密码
   - 使用专门的测试账号

2. **API Key安全**
   - 妥善保管生成的API Key
   - 不要将API Key提交到版本控制
   - 定期轮换API Key

3. **使用限制**
   - 遵守OneRouter的使用条款
   - 不要过于频繁地请求
   - 合理设置等待时间

## 📞 技术支持

如果遇到问题：

1. 首先运行 `python test_config.py` 检查配置
2. 查看生成的错误日志文件
3. 检查网络连接和账号状态
4. 尝试手动执行一次流程确认步骤

## 🎉 成功示例

正常运行时的输出：
```
🚀 OneRouter API Key 批量获取工具 (增强版)
============================================================
📖 读取到 3 个账号

==================================================
🚀 开始处理账号: <EMAIL>
==================================================
🔐 开始登录谷歌账号: <EMAIL>
✅ 谷歌登录成功: <EMAIL>
🌐 开始登录OneRouter平台
✅ OneRouter登录成功
🔑 开始创建API Key
✅ API Key创建成功: sk-1TD4PFwiRQ2tU08E...
💾 API Key已保存到: api_keys/api_key_user1_gmail_com_20250630_153045.txt
🗑️  已从账号列表中删除: <EMAIL>
🎉 账号 <EMAIL> 处理完成！

============================================================
🎯 执行完成！
✅ 成功: 3 个账号
❌ 失败: 0 个账号
📁 结果文件保存在: api_keys
============================================================
```

---

**祝您使用愉快！** 🚀
