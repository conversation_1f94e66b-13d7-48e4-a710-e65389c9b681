#!/usr/bin/env node
/**
 * OneRouter 自动化命令生成器
 */

const fs = require('fs').promises;

async function main() {
    try {
        // 读取下一个账号
        const content = await fs.readFile('卡密信息_XJL2506300700BXW78.txt', 'utf-8');
        const lines = content.split('\n').filter(line => line.trim() && line.startsWith('卡密：'));
        
        if (lines.length === 0) {
            console.log('❌ 没有更多待处理的账号');
            return;
        }

        const firstLine = lines[0];
        const cardInfo = firstLine.replace('卡密：', '').trim();
        const parts = cardInfo.split('----');
        
        if (parts.length < 2) {
            console.log('❌ 账号格式错误');
            return;
        }

        const account = {
            email: parts[0].trim(),
            password: parts[1].trim(),
            auxiliary: parts[2] ? parts[2].trim() : ''
        };

        const keyName = `${account.email.split('@')[0]} Main Key`;
        
        console.log('🤖 OneRouter 自动化命令生成器');
        console.log('='.repeat(60));
        console.log(`📧 账号: ${account.email}`);
        console.log(`🔑 密码: ${account.password}`);
        console.log(`📨 辅助邮箱: ${account.auxiliary}`);
        console.log(`🏷️  API Key 名称: ${keyName}`);
        console.log('='.repeat(60));
        
        console.log('\n请在 Claude 4.0 sonnet 中按顺序执行以下 MCP 调用：\n');

        // 生成完整的命令序列
        const commands = `
## 步骤 1: 关闭浏览器
<function_calls>
<invoke name="browser_close_playwright">
