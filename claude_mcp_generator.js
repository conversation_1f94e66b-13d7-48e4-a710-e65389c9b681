#!/usr/bin/env node
/**
 * <PERSON> MCP 命令生成器
 * 生成可以直接复制粘贴到 Claude 中执行的 MCP 调用序列
 */

const fs = require('fs').promises;

class ClaudeMcpGenerator {
    constructor() {
        this.cardFile = '卡密信息_XJL2506300700BXW78.txt';
    }

    async getNextAccount() {
        try {
            const content = await fs.readFile(this.cardFile, 'utf-8');
            const lines = content.split('\n').filter(line => line.trim() && line.startsWith('卡密：'));
            
            if (lines.length === 0) {
                return null;
            }

            const firstLine = lines[0];
            const cardInfo = firstLine.replace('卡密：', '').trim();
            const parts = cardInfo.split('----');
            
            if (parts.length >= 2) {
                return {
                    email: parts[0].trim(),
                    password: parts[1].trim(),
                    auxiliary: parts[2] ? parts[2].trim() : '',
                    original: firstLine
                };
            }
            
            return null;
        } catch (error) {
            console.error('❌ 读取卡密文件失败:', error.message);
            return null;
        }
    }

    generateMcpCommands(account) {
        const keyName = `${account.email.split('@')[0]} Main Key`;
        
        return `
# OneRouter 自动化 MCP 命令序列
账号: ${account.email}
密码: ${account.password}
API Key 名称: ${keyName}

## 步骤 1: 关闭浏览器
<function_calls>
<invoke name="browser_close_playwright">
