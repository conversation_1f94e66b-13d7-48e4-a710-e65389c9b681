#!/usr/bin/env node
/**
 * Claude 4.0 sonnet 自动化指令生成器
 * 生成可以直接在 Claude 中执行的 MCP 调用序列
 */

const fs = require('fs').promises;

class ClaudeAutomationGenerator {
    constructor() {
        this.cardFile = '卡密信息_XJL2506300700BXW78.txt';
        this.apiKeyFile = 'onerouter_api_key.txt';
        this.outputFile = 'claude_automation_script.md';
    }

    /**
     * 读取下一个待处理账号
     */
    async getNextAccount() {
        try {
            const content = await fs.readFile(this.cardFile, 'utf-8');
            const lines = content.split('\n').filter(line => line.trim() && line.startsWith('卡密：'));
            
            if (lines.length === 0) {
                return null;
            }

            const firstLine = lines[0];
            const cardInfo = firstLine.replace('卡密：', '').trim();
            const parts = cardInfo.split('----');
            
            if (parts.length >= 2) {
                return {
                    email: parts[0].trim(),
                    password: parts[1].trim(),
                    auxiliary: parts[2] ? parts[2].trim() : '',
                    original: firstLine
                };
            }
            
            return null;
        } catch (error) {
            console.error('❌ 读取卡密文件失败:', error.message);
            return null;
        }
    }

    /**
     * 生成 Claude MCP 调用脚本
     */
    async generateClaudeScript(account) {
        const timestamp = new Date().toISOString();
        const keyName = `${account.email.split('@')[0]} Main Key`;
        
        const script = `# OneRouter 自动化脚本 - ${account.email}
生成时间: ${timestamp}

## 账号信息
- **邮箱**: ${account.email}
- **密码**: ${account.password}
- **辅助邮箱**: ${account.auxiliary}
- **API Key 名称**: ${keyName}

## Claude 4.0 sonnet MCP 调用序列

请按顺序执行以下 MCP 调用：

### 步骤 1: 关闭浏览器
\`\`\`xml
<function_calls>
<invoke name="browser_close_playwright">
