{"files": {"cardFile": "卡密信息_XJL2506300700BXW78.txt", "apiKeyFile": "onerouter_api_key.txt", "accountsFile": "accounts.txt"}, "settings": {"targetCount": 5, "delayBetweenAccounts": 10000, "pageLoadTimeout": 10000, "actionTimeout": 5000}, "urls": {"googleSignin": "https://accounts.google.com/signin", "googleLogout": "https://accounts.google.com/logout", "oneRouterLogin": "https://app.onerouter.pro/login"}, "selectors": {"google": {"useOtherAccount": "使用其他账号", "emailInput": "电子邮件地址或电话号码", "passwordInput": "输入您的密码", "nextButton": "下一步", "continueButton": "Continue"}, "oneRouter": {"signInWithGoogle": "Sign in with Google", "createApiKeyStart": "START", "apiKeyNameInput": "e.g. <PERSON><PERSON><PERSON>", "createButton": "Create", "confirmButton": "Confirm"}}, "workflow": {"steps": [{"name": "关闭浏览器", "action": "browser_close_playwright", "description": "确保新会话"}, {"name": "退出当前账号", "action": "browser_navigate_playwright", "params": {"url": "googleLogout"}, "description": "退出之前登录的账号"}, {"name": "选择使用其他账号", "action": "browser_click_playwright", "params": {"element": "google.useOtherAccount"}, "description": "点击使用其他账号"}, {"name": "输入邮箱", "action": "browser_type_playwright", "params": {"element": "google.emailInput", "text": "{email}"}, "description": "输入谷歌邮箱地址"}, {"name": "点击下一步", "action": "browser_click_playwright", "params": {"element": "google.nextButton"}, "description": "进入密码输入页面"}, {"name": "输入密码", "action": "browser_type_playwright", "params": {"element": "google.passwordInput", "text": "{password}"}, "description": "输入谷歌账号密码"}, {"name": "登录谷歌", "action": "browser_click_playwright", "params": {"element": "google.nextButton"}, "description": "完成谷歌登录"}, {"name": "等待登录完成", "action": "browser_wait_for_playwright", "params": {"time": 5}, "description": "等待谷歌登录完成"}, {"name": "访问OneRouter", "action": "browser_navigate_playwright", "params": {"url": "oneRout<PERSON><PERSON><PERSON><PERSON>"}, "description": "访问OneRouter登录页面"}, {"name": "使用谷歌登录", "action": "browser_click_playwright", "params": {"element": "oneRouter.signInWithGoogle"}, "description": "点击谷歌登录按钮"}, {"name": "等待OAuth跳转", "action": "browser_wait_for_playwright", "params": {"time": 5}, "description": "等待OAuth页面加载"}, {"name": "选择当前账号", "action": "browser_click_playwright", "params": {"element": "currentAccount"}, "description": "选择当前登录的谷歌账号"}, {"name": "授权OneRouter", "action": "browser_click_playwright", "params": {"element": "google.continueButton"}, "description": "授权OneRouter访问谷歌账号"}, {"name": "等待OneRouter登录", "action": "browser_wait_for_playwright", "params": {"time": 10}, "description": "等待OneRouter登录完成"}, {"name": "开始创建API Key", "action": "browser_click_playwright", "params": {"element": "oneRouter.createApiKeyStart"}, "description": "点击创建API Key的START按钮"}, {"name": "输入API Key名称", "action": "browser_type_playwright", "params": {"element": "oneRouter.apiKeyNameInput", "text": "{keyName}"}, "description": "输入API Key的名称"}, {"name": "创建API Key", "action": "browser_click_playwright", "params": {"element": "oneRouter.createButton"}, "description": "点击Create按钮创建API Key"}, {"name": "等待API Key生成", "action": "browser_wait_for_playwright", "params": {"time": 3}, "description": "等待API Key生成完成"}, {"name": "提取API Key", "action": "extract_api_key", "description": "从页面中提取生成的API Key"}, {"name": "确认API Key", "action": "browser_click_playwright", "params": {"element": "oneRouter.confirmButton"}, "description": "点击Confirm按钮完成创建"}]}, "logging": {"level": "info", "enableTimestamp": true, "enableColors": true}, "errorHandling": {"maxRetries": 3, "retryDelay": 5000, "continueOnError": true}}