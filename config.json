{"settings": {"accounts_file": "accounts.txt", "output_directory": "api_keys", "browser_headless": false, "wait_between_accounts": 10, "timeout_seconds": 30, "max_retries": 3}, "urls": {"google_login": "https://accounts.google.com/signin", "onerouter_login": "https://app.onerouter.pro/login", "onerouter_apikeys": "https://app.onerouter.pro/apiKeys"}, "selectors": {"google_email_input": "input[type=\"email\"]", "google_password_input": "input[type=\"password\"]:not([aria-hidden=\"true\"]), input[name=\"password\"], input[autocomplete=\"current-password\"]", "google_next_button": "button:has-text(\"下一步\"), button:has-text(\"Next\")", "onerouter_google_login": "button:has-text(\"Sign in with Google\")", "onerouter_continue": "button:has-text(\"Continue\"), button:has-text(\"继续\")", "create_key_button": "button:has-text(\"N\"), button[aria-label*=\"new\"]", "key_name_input": "input[placeholder*=\"Chatbot\"], input[placeholder*=\"Key\"]", "create_submit": "button:has-text(\"Create\"), button:has-text(\"创建\")", "api_key_display": "input[value*=\"sk-\"], textarea[value*=\"sk-\"]", "confirm_button": "button:has-text(\"Confirm\"), button:has-text(\"确认\")"}}