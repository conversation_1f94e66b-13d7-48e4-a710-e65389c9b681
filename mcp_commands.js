#!/usr/bin/env node
const fs = require('fs').promises;

async function main() {
    try {
        const content = await fs.readFile('卡密信息_XJL2506300700BXW78.txt', 'utf-8');
        const lines = content.split('\n').filter(line => line.trim() && line.startsWith('卡密：'));
        
        if (lines.length === 0) {
            console.log('❌ 没有更多待处理的账号');
            return;
        }

        const firstLine = lines[0];
        const cardInfo = firstLine.replace('卡密：', '').trim();
        const parts = cardInfo.split('----');
        
        const account = {
            email: parts[0].trim(),
            password: parts[1].trim(),
            auxiliary: parts[2] ? parts[2].trim() : ''
        };

        const keyName = account.email.split('@')[0] + ' Main Key';
        
        console.log('🤖 OneRouter 自动化 MCP 命令');
        console.log('='.repeat(50));
        console.log('账号:', account.email);
        console.log('密码:', account.password);
        console.log('API Key 名称:', keyName);
        console.log('='.repeat(50));
        console.log('\n请复制以下命令到 Claude 中执行：\n');

        // 步骤1: 关闭浏览器
        console.log('## 步骤1: 关闭浏览器');
        console.log('<function_calls>');
        console.log('<invoke name="browser_close_playwright">
