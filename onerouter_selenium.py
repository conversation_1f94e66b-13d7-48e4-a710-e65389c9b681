#!/usr/bin/env python3
"""
OneRouter API Key 自动化获取脚本 (Selenium版本)
使用反检测技术避免触发验证码
"""

import time
import json
import os
from datetime import datetime
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class OneRouterSelenium:
    def __init__(self):
        self.output_dir = Path("api_keys")
        self.output_dir.mkdir(exist_ok=True)
        self.success_log = []
        self.failed_log = []
        
    def create_driver(self):
        """创建反检测的Chrome驱动"""
        options = Options()
        
        # 反检测设置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--disable-extensions')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--remote-debugging-port=9222')
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 禁用图片加载以提高速度
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2
        }
        options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=options)
            
            # 执行反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return driver
        except Exception as e:
            print(f"❌ 创建浏览器驱动失败: {e}")
            print("💡 请确保已安装 ChromeDriver")
            return None
    
    def read_accounts(self):
        """读取账号信息"""
        accounts = []
        if not os.path.exists("accounts.txt"):
            print("❌ 账号文件 accounts.txt 不存在")
            return accounts
            
        with open("accounts.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            if '----' in line:
                parts = line.split('----')
            elif ':' in line:
                parts = line.split(':')
            else:
                continue
                
            if len(parts) >= 2:
                email = parts[0].strip()
                password = parts[1].strip()
                accounts.append({'email': email, 'password': password})
                
        print(f"📖 读取到 {len(accounts)} 个账号")
        return accounts
    
    def manual_login_google(self, driver, email, password):
        """手动辅助的谷歌登录"""
        try:
            print(f"🔐 开始登录谷歌账号: {email}")
            
            # 访问谷歌登录页面
            driver.get("https://accounts.google.com/signin")
            time.sleep(3)
            
            # 输入邮箱
            email_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[type="email"]'))
            )
            email_input.send_keys(email)
            
            # 点击下一步
            next_button = driver.find_element(By.XPATH, '//span[text()="下一步" or text()="Next"]/parent::button')
            next_button.click()
            time.sleep(3)
            
            # 输入密码
            password_input = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'input[type="password"]:not([aria-hidden="true"])'))
            )
            password_input.send_keys(password)
            
            # 点击下一步
            next_button = driver.find_element(By.XPATH, '//span[text()="下一步" or text()="Next"]/parent::button')
            next_button.click()
            
            # 等待登录完成或验证码
            print("⏳ 等待登录完成...")
            print("💡 如果出现验证码，请手动完成验证")
            
            # 等待跳转到谷歌账户页面
            WebDriverWait(driver, 60).until(
                lambda d: "myaccount.google.com" in d.current_url or "accounts.google.com" in d.current_url
            )
            
            if "myaccount.google.com" in driver.current_url:
                print(f"✅ 谷歌登录成功: {email}")
                return True
            else:
                print("⚠️  可能需要手动处理验证")
                input("请手动完成验证后按回车继续...")
                return True
                
        except Exception as e:
            print(f"❌ 谷歌登录失败: {email} - {str(e)}")
            return False
    
    def login_onerouter(self, driver):
        """登录OneRouter"""
        try:
            print("🌐 开始登录OneRouter平台")
            
            driver.get("https://app.onerouter.pro/login")
            time.sleep(3)
            
            # 点击Google登录
            google_login = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(text(), "Sign in with Google")]'))
            )
            google_login.click()
            time.sleep(3)
            
            # 处理Google授权
            if "accounts.google.com" in driver.current_url:
                # 选择账号
                try:
                    account_element = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-identifier]'))
                    )
                    account_element.click()
                    time.sleep(2)
                except:
                    pass
                
                # 点击继续
                try:
                    continue_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, '//span[text()="Continue" or text()="继续"]/parent::button'))
                    )
                    continue_button.click()
                    time.sleep(3)
                except:
                    pass
            
            # 等待跳转到OneRouter
            WebDriverWait(driver, 30).until(
                lambda d: "app.onerouter.pro" in d.current_url and "login" not in d.current_url
            )
            
            print("✅ OneRouter登录成功")
            return True
            
        except Exception as e:
            print(f"❌ OneRouter登录失败: {str(e)}")
            return False
    
    def create_api_key(self, driver, email):
        """创建API Key"""
        try:
            print("🔑 开始创建API Key")
            
            # 导航到API Keys页面
            driver.get("https://app.onerouter.pro/apiKeys")
            time.sleep(3)
            
            # 点击创建按钮
            create_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(@aria-label, "new") or contains(text(), "N")]'))
            )
            create_button.click()
            time.sleep(2)
            
            # 输入名称
            key_name = f"Auto Key {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            name_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder*="Key"], input[placeholder*="Chatbot"]'))
            )
            name_input.send_keys(key_name)
            
            # 点击创建
            create_submit = driver.find_element(By.XPATH, '//button[contains(text(), "Create") or contains(text(), "创建")]')
            create_submit.click()
            time.sleep(3)
            
            # 获取API Key
            api_key_element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[value*="sk-"], textarea[value*="sk-"]'))
            )
            api_key = api_key_element.get_attribute('value')
            
            print(f"✅ API Key创建成功: {api_key[:20]}...")
            
            # 点击确认
            try:
                confirm_button = driver.find_element(By.XPATH, '//button[contains(text(), "Confirm") or contains(text(), "确认")]')
                confirm_button.click()
            except:
                pass
            
            return api_key
            
        except Exception as e:
            print(f"❌ 创建API Key失败: {str(e)}")
            return None
    
    def save_api_key(self, email, api_key):
        """保存API Key"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"api_key_{email.replace('@', '_').replace('.', '_')}_{timestamp}.txt"
        filepath = self.output_dir / filename
        
        content = f"""OneRouter API Key (Selenium版本)
===================

Account: {email}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Status: Active

API Key: {api_key}

Note: This key was generated using Selenium automation.
Please keep this key secure and do not share it publicly.
"""
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
            
        print(f"💾 API Key已保存到: {filepath}")
        return filepath
    
    def remove_processed_account(self, email):
        """从账号列表删除已处理的账号"""
        if not os.path.exists("accounts.txt"):
            return
            
        with open("accounts.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        filtered_lines = [line for line in lines if email not in line]
        
        with open("accounts.txt", 'w', encoding='utf-8') as f:
            f.writelines(filtered_lines)
            
        print(f"🗑️  已从账号列表中删除: {email}")
    
    def run(self):
        """主执行函数"""
        print("🤖 OneRouter API Key 自动化获取工具 (Selenium版本)")
        print("=" * 60)
        
        accounts = self.read_accounts()
        if not accounts:
            print("❌ 没有找到可处理的账号")
            return
        
        driver = self.create_driver()
        if not driver:
            return
        
        try:
            success_count = 0
            failed_count = 0
            
            for i, account in enumerate(accounts, 1):
                email = account['email']
                password = account['password']
                
                print(f"\n📋 进度: {i}/{len(accounts)}")
                print(f"{'='*50}")
                print(f"🚀 开始处理账号: {email}")
                print(f"{'='*50}")
                
                try:
                    # 1. 登录谷歌
                    if not self.manual_login_google(driver, email, password):
                        failed_count += 1
                        continue
                    
                    # 2. 登录OneRouter
                    if not self.login_onerouter(driver):
                        failed_count += 1
                        continue
                    
                    # 3. 创建API Key
                    api_key = self.create_api_key(driver, email)
                    if not api_key:
                        failed_count += 1
                        continue
                    
                    # 4. 保存结果
                    self.save_api_key(email, api_key)
                    self.remove_processed_account(email)
                    
                    success_count += 1
                    print(f"🎉 账号 {email} 处理完成！")
                    
                except Exception as e:
                    print(f"❌ 处理账号 {email} 时发生错误: {str(e)}")
                    failed_count += 1
                
                # 等待一段时间
                if i < len(accounts):
                    print("⏳ 等待10秒后处理下一个账号...")
                    time.sleep(10)
            
            print(f"\n{'='*60}")
            print(f"🎯 执行完成！")
            print(f"✅ 成功: {success_count} 个账号")
            print(f"❌ 失败: {failed_count} 个账号")
            print(f"{'='*60}")
            
        finally:
            driver.quit()


if __name__ == "__main__":
    automation = OneRouterSelenium()
    automation.run()
