#!/usr/bin/env node
/**
 * 处理下一个账号的简化脚本
 * 基于验证过的工作流程，处理卡密文件中的下一个账号
 */

const fs = require('fs').promises;

class NextAccountProcessor {
    constructor() {
        this.cardFile = '卡密信息_XJL2506300700BXW78.txt';
        this.apiKeyFile = 'onerouter_api_key.txt';
    }

    /**
     * 读取下一个待处理账号
     */
    async getNextAccount() {
        try {
            const content = await fs.readFile(this.cardFile, 'utf-8');
            const lines = content.split('\n').filter(line => line.trim() && line.startsWith('卡密：'));
            
            if (lines.length === 0) {
                return null;
            }

            // 获取第一个账号（下一个待处理的）
            const firstLine = lines[0];
            const cardInfo = firstLine.replace('卡密：', '').trim();
            const parts = cardInfo.split('----');
            
            if (parts.length >= 2) {
                return {
                    email: parts[0].trim(),
                    password: parts[1].trim(),
                    auxiliary: parts[2] ? parts[2].trim() : '',
                    original: firstLine
                };
            }
            
            return null;
        } catch (error) {
            console.error('❌ 读取卡密文件失败:', error.message);
            return null;
        }
    }

    /**
     * 显示下一个账号信息
     */
    async showNextAccount() {
        console.log('🔍 OneRouter 下一个账号处理器');
        console.log('=' * 50);

        const account = await this.getNextAccount();
        
        if (!account) {
            console.log('❌ 没有找到待处理的账号');
            console.log('📁 请检查卡密文件是否存在且包含有效账号信息');
            return;
        }

        console.log('📋 下一个待处理账号信息：');
        console.log(`📧 主邮箱: ${account.email}`);
        console.log(`🔑 密码: ${account.password}`);
        console.log(`📨 辅助邮箱: ${account.auxiliary}`);
        console.log('');

        // 显示处理指令
        console.log('🚀 手动处理步骤：');
        console.log('1. 关闭浏览器，开启新会话');
        console.log('2. 访问: https://accounts.google.com/logout');
        console.log('3. 点击"使用其他账号"');
        console.log(`4. 输入邮箱: ${account.email}`);
        console.log('5. 点击"下一步"');
        console.log(`6. 输入密码: ${account.password}`);
        console.log('7. 点击"下一步"完成谷歌登录');
        console.log('8. 访问: https://app.onerouter.pro/login');
        console.log('9. 点击"Sign in with Google"');
        console.log('10. 选择当前账号并点击"Continue"');
        console.log('11. 点击"START"创建API Key');
        console.log(`12. 输入名称: ${account.email.split('@')[0]} Main Key`);
        console.log('13. 点击"Create"');
        console.log('14. 复制生成的API Key');
        console.log('15. 点击"Confirm"');
        console.log('');

        console.log('📝 完成后请运行以下命令更新记录：');
        console.log(`node update_records.js "${account.email}" "YOUR_API_KEY_HERE"`);
        
        return account;
    }

    /**
     * 获取当前统计信息
     */
    async getStats() {
        try {
            // 读取卡密文件统计剩余账号
            const cardContent = await fs.readFile(this.cardFile, 'utf-8');
            const remainingAccounts = cardContent.split('\n').filter(line => line.trim() && line.startsWith('卡密：')).length;

            // 读取API Key文件统计已完成账号
            let completedAccounts = 0;
            try {
                const apiContent = await fs.readFile(this.apiKeyFile, 'utf-8');
                completedAccounts = (apiContent.match(/# Account \d+:/g) || []).length;
            } catch (error) {
                // API Key文件不存在，说明还没有完成任何账号
                completedAccounts = 0;
            }

            console.log('📊 当前进度统计：');
            console.log(`✅ 已完成: ${completedAccounts} 个账号`);
            console.log(`📋 剩余: ${remainingAccounts} 个账号`);
            console.log(`🎯 总计: ${completedAccounts + remainingAccounts} 个账号`);
            
        } catch (error) {
            console.error('❌ 获取统计信息失败:', error.message);
        }
    }
}

// 创建更新记录的脚本内容
const updateRecordsScript = `#!/usr/bin/env node
/**
 * 更新处理记录脚本
 * 用法: node update_records.js "email" "api_key"
 */

const fs = require('fs').promises;

async function updateRecords(email, apiKey) {
    const cardFile = '卡密信息_XJL2506300700BXW78.txt';
    const apiKeyFile = 'onerouter_api_key.txt';
    
    try {
        // 1. 从卡密文件中删除已处理账号
        const cardContent = await fs.readFile(cardFile, 'utf-8');
        const lines = cardContent.split('\\n');
        const filteredLines = lines.filter(line => !line.includes(email));
        await fs.writeFile(cardFile, filteredLines.join('\\n'), 'utf-8');
        console.log('🗑️  已从卡密文件中删除账号');

        // 2. 添加API Key到记录文件
        const timestamp = new Date().toISOString().split('T')[0];
        const keyName = email.split('@')[0] + ' Main Key';
        
        let apiContent = '';
        try {
            apiContent = await fs.readFile(apiKeyFile, 'utf-8');
        } catch (error) {
            apiContent = \`OneRouter API Key Collection
============================

\`;
        }

        // 计算账号编号
        const accountCount = (apiContent.match(/# Account \\d+:/g) || []).length + 1;
        
        const newEntry = \`
# Account \${accountCount}: \${email} (\${keyName.split(' ')[0]} \${keyName.split(' ')[1]}) ✅ 已完成
Generated: \${timestamp}
Key Name: \${keyName}
Status: Active
API Key: \${apiKey}
\`;

        const noteIndex = apiContent.indexOf('Note: These keys were automatically generated');
        if (noteIndex !== -1) {
            const beforeNote = apiContent.substring(0, noteIndex);
            const afterNote = apiContent.substring(noteIndex);
            apiContent = beforeNote + newEntry + '\\n' + afterNote;
        } else {
            apiContent += newEntry + '\\n\\nNote: These keys were automatically generated through browser automation.\\nPlease keep these keys secure and do not share them publicly.\\n';
        }

        await fs.writeFile(apiKeyFile, apiContent, 'utf-8');
        console.log('💾 API Key 已保存');
        
        console.log(\`🎉 账号 \${email} 处理完成！\`);
        
    } catch (error) {
        console.error('❌ 更新记录失败:', error.message);
    }
}

if (process.argv.length < 4) {
    console.log('用法: node update_records.js "email" "api_key"');
    process.exit(1);
}

const email = process.argv[2];
const apiKey = process.argv[3];

updateRecords(email, apiKey);
`;

// 程序入口
async function main() {
    const processor = new NextAccountProcessor();
    
    // 显示统计信息
    await processor.getStats();
    console.log('');
    
    // 显示下一个账号
    await processor.showNextAccount();
    
    // 创建更新记录脚本
    try {
        await fs.writeFile('update_records.js', updateRecordsScript, 'utf-8');
        console.log('📝 已创建 update_records.js 脚本');
    } catch (error) {
        console.error('❌ 创建更新脚本失败:', error.message);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = NextAccountProcessor;
