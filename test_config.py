#!/usr/bin/env python3
"""
配置文件测试脚本
"""

import json
import sys
from onerouter_automation_enhanced import OneRouterAutomationEnhanced

def test_config():
    """测试配置文件加载"""
    print("🧪 测试配置文件...")
    
    try:
        automation = OneRouterAutomationEnhanced()
        config = automation.config
        
        print("✅ 配置文件加载成功")
        print(f"📁 账号文件: {config['settings']['accounts_file']}")
        print(f"📁 输出目录: {config['settings']['output_directory']}")
        print(f"🖥️  无头模式: {config['settings']['browser_headless']}")
        print(f"⏱️  账号间等待: {config['settings']['wait_between_accounts']}秒")
        print(f"⏱️  超时时间: {config['settings']['timeout_seconds']}秒")
        print(f"🔄 最大重试: {config['settings']['max_retries']}次")
        
        # 测试账号读取
        accounts = automation.read_accounts()
        print(f"📖 找到 {len(accounts)} 个账号")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_syntax():
    """测试脚本语法"""
    print("🧪 测试脚本语法...")
    
    scripts = [
        "onerouter_automation.py",
        "onerouter_automation_enhanced.py"
    ]
    
    for script in scripts:
        try:
            with open(script, 'r', encoding='utf-8') as f:
                compile(f.read(), script, 'exec')
            print(f"✅ {script} 语法正确")
        except SyntaxError as e:
            print(f"❌ {script} 语法错误: {e}")
            return False
        except FileNotFoundError:
            print(f"⚠️  {script} 文件不存在")
    
    return True

if __name__ == "__main__":
    print("🚀 OneRouter 自动化脚本测试")
    print("=" * 40)
    
    success = True
    success &= test_syntax()
    success &= test_config()
    
    if success:
        print("\n🎉 所有测试通过！脚本可以正常运行")
    else:
        print("\n❌ 测试失败，请检查配置")
        sys.exit(1)
