# OneRouter API Key 自动化获取工具

这是一个自动化脚本，可以批量处理多个谷歌账号，自动登录 OneRouter 平台并获取 API Key。

## 功能特点

- 🤖 **全自动化**: 无需人工干预，自动完成整个流程
- 📋 **批量处理**: 支持同时处理多个账号
- 🗑️ **智能清理**: 成功获取API Key后自动从账号列表中删除
- 📊 **详细日志**: 记录成功和失败的详细信息
- 💾 **安全存储**: API Key安全保存到独立文件
- ⚡ **错误处理**: 完善的异常处理机制

## 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium
```

## 使用方法

### 1. 准备账号文件

首次运行会自动创建 `accounts.txt` 文件，格式如下：

```
# OneRouter 账号列表
# 格式: 邮箱----密码 或 邮箱:密码

<EMAIL>----3gr124ih
<EMAIL>:password123
<EMAIL>----password456
```

### 2. 运行脚本

```bash
python onerouter_automation.py
```

### 3. 查看结果

- API Key 文件保存在 `api_keys/` 目录
- 执行日志保存为 JSON 格式
- 成功处理的账号会从 `accounts.txt` 中自动删除

## 文件结构

```
├── onerouter_automation.py    # 主脚本
├── accounts.txt              # 账号列表文件
├── requirements.txt          # Python依赖
├── README.md                # 使用说明
└── api_keys/                # 输出目录
    ├── api_key_xxx.txt      # API Key文件
    ├── success_log_xxx.json # 成功日志
    └── failed_log_xxx.json  # 失败日志
```

## 执行流程

1. **读取账号**: 从 `accounts.txt` 读取账号列表
2. **谷歌登录**: 自动登录谷歌账号
3. **OneRouter登录**: 使用谷歌OAuth登录OneRouter
4. **创建API Key**: 自动创建新的API Key
5. **保存结果**: 将API Key保存到文件
6. **清理账号**: 从列表中删除已处理的账号
7. **记录日志**: 保存执行结果和错误信息

## 注意事项

- 🔒 请确保账号信息的安全性
- 🌐 需要稳定的网络连接
- ⏱️ 每个账号之间有10秒延迟，避免被检测
- 🖥️ 默认显示浏览器窗口，可修改为无头模式
- 📱 如果账号开启了二次验证，可能需要手动处理

## 自定义配置

可以在脚本中修改以下参数：

```python
# 修改账号文件路径
automation = OneRouterAutomation(accounts_file="my_accounts.txt")

# 修改输出目录
automation = OneRouterAutomation(output_dir="my_output")

# 修改浏览器模式（无头模式）
browser = await p.chromium.launch(headless=True)
```

## 故障排除

1. **登录失败**: 检查账号密码是否正确
2. **网络超时**: 检查网络连接，增加超时时间
3. **元素定位失败**: OneRouter页面可能有更新，需要调整选择器
4. **浏览器启动失败**: 确保已安装Playwright浏览器

## 作者

Created by **Claude 4.0 sonnet** - AI编程助手

---

⚠️ **免责声明**: 此工具仅供学习和研究使用，请遵守相关网站的使用条款。
