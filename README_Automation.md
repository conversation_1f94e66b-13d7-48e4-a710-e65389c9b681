# OneRouter API Key 自动化获取工具

## 📋 概述

这是一个基于验证过的工作流程开发的 JavaScript 自动化脚本，用于批量获取 OneRouter API Key。该脚本使用本地 MCP (Model Context Protocol) 工具进行浏览器自动化操作。

## 🔧 工作流程

### 验证过的标准流程：
1. **关闭浏览器** - 确保新会话，避免缓存问题
2. **退出当前账号** - 访问谷歌退出页面
3. **登录谷歌账号** - 输入邮箱和密码
4. **访问 OneRouter** - 导航到登录页面
5. **使用谷歌 OAuth** - 授权 OneRouter 访问
6. **创建 API Key** - 输入名称并生成
7. **保存结果** - 更新文件并删除已处理账号

## 📁 文件结构

```
├── onerouter_automation.js      # 主自动化脚本
├── automation_config.json       # 配置文件
├── 卡密信息_XJL2506300700BXW78.txt  # 账号信息文件
├── onerouter_api_key.txt        # API Key 保存文件
├── accounts.txt                 # 账号处理记录
└── README_Automation.md         # 使用说明
```

## 🚀 使用方法

### 1. 环境准备
```bash
# 确保安装了 Node.js (版本 14+)
node --version

# 安装依赖（如果有）
npm install
```

### 2. 配置文件
编辑 `automation_config.json` 调整以下设置：
- `targetCount`: 要处理的账号数量
- `delayBetweenAccounts`: 账号间处理间隔（毫秒）
- `pageLoadTimeout`: 页面加载超时时间
- `actionTimeout`: 操作超时时间

### 3. 运行脚本
```bash
# 运行自动化脚本
node onerouter_automation.js

# 或者使其可执行
chmod +x onerouter_automation.js
./onerouter_automation.js
```

## 📊 输出示例

```
🤖 OneRouter API Key 自动化获取工具 (JavaScript版本)
============================================================
📖 读取到 344 个账号
🎯 目标处理 5 个账号

============================================================
🚀 开始处理账号: <EMAIL>
🔑 密码: qq25s51te7
📧 辅助邮箱: <EMAIL>
============================================================

📋 步骤1: 关闭浏览器，开启新会话
🔧 MCP调用: browser_close_playwright

📋 步骤2: 登录谷歌账号
🔧 MCP调用: browser_navigate_playwright { url: 'https://accounts.google.com/logout' }
🔧 MCP调用: browser_click_playwright { element: '使用其他账号' }
🔧 MCP调用: browser_type_playwright { element: '邮箱输入框', text: '<EMAIL>' }

📋 步骤3: 访问 OneRouter 登录页面
🔧 MCP调用: browser_navigate_playwright { url: 'https://app.onerouter.pro/login' }

📋 步骤4: 创建 API Key
🔧 MCP调用: browser_click_playwright { element: 'Create new API Key START 按钮' }
✅ 找到 API Key: sk-9rzt3o9fMainKey...

📋 步骤5: 保存结果
💾 API Key 已保存到: onerouter_api_key.txt

📋 步骤6: 删除已处理账号
🗑️  已从卡密文件中删除已处理账号

🎉 账号 <EMAIL> 处理完成！
📊 进度: 1/5

============================================================
🎯 执行完成！
✅ 成功: 5 个账号
❌ 失败: 0 个账号
📁 结果文件: onerouter_api_key.txt
============================================================
```

## ⚙️ MCP 工具集成

### 需要的 MCP 工具：
1. **browser_close_playwright** - 关闭浏览器
2. **browser_navigate_playwright** - 页面导航
3. **browser_click_playwright** - 点击元素
4. **browser_type_playwright** - 输入文本
5. **browser_wait_for_playwright** - 等待操作
6. **browser_snapshot_playwright** - 获取页面快照

### MCP 调用示例：
```javascript
// 导航到页面
await mcpCall('browser_navigate_playwright', {
    url: 'https://accounts.google.com/signin'
});

// 点击元素
await mcpCall('browser_click_playwright', {
    element: '使用其他账号',
    ref: 'e56'
});

// 输入文本
await mcpCall('browser_type_playwright', {
    element: '邮箱输入框',
    ref: 'e130',
    text: '<EMAIL>'
});
```

## 🔍 故障排除

### 常见问题：

1. **MCP 工具未响应**
   - 检查 MCP 服务是否正常运行
   - 确认工具权限配置正确

2. **页面元素找不到**
   - 页面结构可能发生变化
   - 需要更新配置文件中的选择器

3. **登录失败**
   - 检查账号密码是否正确
   - 可能需要处理验证码或安全检查

4. **API Key 提取失败**
   - 页面加载可能需要更长时间
   - 增加等待时间或改进提取逻辑

### 调试模式：
```javascript
// 在配置文件中启用详细日志
{
  "logging": {
    "level": "debug",
    "enableTimestamp": true,
    "enableColors": true
  }
}
```

## 📈 性能优化

1. **并发处理** - 可以修改脚本支持多账号并发处理
2. **错误重试** - 配置文件中已包含重试机制
3. **资源管理** - 及时关闭浏览器实例释放资源

## 🔒 安全注意事项

1. **账号信息保护** - 确保卡密文件安全存储
2. **API Key 安全** - 生成的 API Key 应妥善保管
3. **访问频率** - 避免过于频繁的请求被检测为异常

## 📝 自定义扩展

### 添加新的处理步骤：
1. 在 `automation_config.json` 的 `workflow.steps` 中添加新步骤
2. 在 `onerouter_automation.js` 中实现对应的处理逻辑
3. 测试新功能确保稳定性

### 支持其他平台：
脚本架构支持扩展到其他需要类似自动化流程的平台，只需：
1. 修改 URL 配置
2. 更新选择器配置
3. 调整工作流程步骤

## 📞 技术支持

如遇到问题，请检查：
1. 日志输出中的错误信息
2. 配置文件是否正确
3. MCP 工具是否正常工作
4. 网络连接是否稳定

---

**注意**: 此工具仅用于合法的自动化任务，请遵守相关网站的使用条款和法律法规。
